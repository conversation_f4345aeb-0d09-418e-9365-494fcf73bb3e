"""
PDF Form Data Extractor

This script extracts fillable form data from PDF files using pyPDFForm
and exports the field names and values to an Excel file using openpyxl.

Dependencies:
- pyPDFForm: For PDF form data extraction
- openpyxl: For Excel file creation and manipulation
"""

import os
from PyPDFForm import Pdf<PERSON>rapper
from openpyxl import Workbook


def extract_pdf_form_data(pdf_path):
    """
    Extract all fillable form data from a PDF file.

    Args:
        pdf_path (str): Path to the PDF file

    Returns:
        dict: Dictionary containing field names as keys and values as values
    """
    try:
        # Create PdfWrapper object
        pdf = PdfWrapper(pdf_path)

        # Get all form fields and their values using the data property
        form_data = pdf.data
        schema = pdf.schema

        # Process form data and handle radio buttons specially
        extracted_data = {}
        radio_button_fields = {}

        # First pass: identify radio button fields and regular fields
        for field_name, field_value in form_data.items():
            field_schema = schema['properties'].get(field_name, {})
            field_type = field_schema.get('type', 'unknown')

            # Check if this is a radio button (integer field with maximum value)
            if field_type == 'integer' and 'maximum' in field_schema:
                radio_button_fields[field_name] = {
                    'value': field_value,
                    'maximum': field_schema['maximum'],
                    'description': field_schema.get('description', '')
                }
            else:
                # Regular field - convert to string and handle None/empty values
                if field_value is None:
                    extracted_data[field_name] = ""
                else:
                    extracted_data[field_name] = str(field_value)

        # Second pass: process radio button fields
        if radio_button_fields:
            processed_radio_buttons = process_radio_buttons(radio_button_fields)
            extracted_data.update(processed_radio_buttons)

        return extracted_data

    except Exception as e:
        print(f"Error extracting data from PDF: {e}")
        import traceback
        traceback.print_exc()
        return {}


def process_radio_buttons(radio_button_fields):
    """
    Process radio button fields and convert them to meaningful variable names and values.

    For each radio button field:
    - Uses the field name as the variable name (cleaned up)
    - Maps the numeric value to a descriptive string
    - Sets "noSelection" if no option is selected

    Args:
        radio_button_fields (dict): Dictionary of radio button field information

    Returns:
        dict: Dictionary with processed radio button data
    """
    processed_data = {}

    # Define radio button mappings based on the specific PDF form
    # These mappings can be customized for different forms
    #
    # NOTE: The actual radio buttons visible in Adobe Reader for:
    # - "I hereby certify that I am:"
    # - "INTEREST PAYMENT FREQUENCY"
    # - "INTEREST PAYMENT TIMING"
    # are not being detected by PyPDFForm in this PDF instance.
    #
    # The fields below (p0, p2) are the only integer fields detected.
    # To add mappings for the actual radio buttons, you would need to:
    # 1. Identify their field names (possibly through a different PDF tool)
    # 2. Add them to this mapping dictionary
    # 3. Define the appropriate option values
    #
    # Example format for adding new radio button groups:
    # 'field_name': {
    #     'variable_name': 'descriptive_name',
    #     'options': {
    #         0: 'noSelection',
    #         1: 'option_1_description',
    #         2: 'option_2_description',
    #         # ... etc
    #     }
    # }

    radio_button_mappings = {
        'p0': {
            'variable_name': 'radio_p0_selection',
            'options': {
                0: 'noSelection',
                1: 'option_1',
                2: 'option_2',
                3: 'option_3',
                4: 'option_4',
                5: 'option_5'
            }
        },
        'p2': {
            'variable_name': 'radio_p2_selection',
            'options': {
                0: 'option_0',
                1: 'option_1'
            }
        }
        # TODO: Add mappings for the actual radio buttons when field names are identified:
        # - "I hereby certify that I am:" radio button group
        # - "INTEREST PAYMENT FREQUENCY" radio button group
        # - "INTEREST PAYMENT TIMING" radio button group
    }

    for field_name, field_info in radio_button_fields.items():
        current_value = field_info['value']
        maximum = field_info['maximum']

        if field_name in radio_button_mappings:
            # Use predefined mapping
            mapping = radio_button_mappings[field_name]
            variable_name = mapping['variable_name']
            options = mapping['options']

            # Get the selected option or default to noSelection
            if current_value is not None and current_value in options:
                selected_value = options[current_value]
            else:
                selected_value = 'noSelection'

            processed_data[variable_name] = selected_value
        else:
            # Generic handling for unmapped radio buttons
            # Create a clean variable name from the field name
            variable_name = f"radio_{field_name}".replace(" ", "_").replace("-", "_").replace(".", "_")
            variable_name = "".join(c for c in variable_name if c.isalnum() or c == "_")

            # Create generic option values
            if current_value is not None and 0 <= current_value <= maximum:
                selected_value = f"option_{current_value}"
            else:
                selected_value = 'noSelection'

            processed_data[variable_name] = selected_value

    return processed_data


def create_excel_file(data, output_path):
    """
    Create an Excel file with field names in column A and values in column B.
    
    Args:
        data (dict): Dictionary containing field names and values
        output_path (str): Path for the output Excel file
    """
    try:
        # Create a new workbook and select the active worksheet
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "PDF Form Data"
        
        # Add headers
        worksheet['A1'] = "Field Name"
        worksheet['B1'] = "Field Value"
        
        # Add data starting from row 2
        row = 2
        for field_name, field_value in data.items():
            worksheet[f'A{row}'] = field_name
            worksheet[f'B{row}'] = field_value
            row += 1
        
        # Auto-adjust column widths
        worksheet.column_dimensions['A'].width = 30
        worksheet.column_dimensions['B'].width = 50
        
        # Save the workbook
        workbook.save(output_path)
        print(f"Excel file created successfully: {output_path}")
        
    except Exception as e:
        print(f"Error creating Excel file: {e}")


def main():
    """
    Main function to extract PDF form data and create Excel file.
    """
    # Define file paths
    pdf_file = os.path.join("example forms", "MTM Loan App Starr.pdf")
    excel_output = "extracted_form_data.xlsx"

    # Check if PDF file exists
    if not os.path.exists(pdf_file):
        print(f"Error: PDF file not found at {pdf_file}")
        return

    print(f"Extracting data from: {pdf_file}")

    # Extract form data from PDF
    form_data = extract_pdf_form_data(pdf_file)

    if not form_data:
        print("No form data extracted or error occurred.")
        return

    print(f"Extracted {len(form_data)} form fields")

    # Create string variables for each field (as requested)
    # This creates individual variables for each field
    field_variables = {}
    for field_name, field_value in form_data.items():
        # Create a valid Python variable name from field name
        var_name = field_name.replace(" ", "_").replace("-", "_").replace(".", "_")
        var_name = "".join(c for c in var_name if c.isalnum() or c == "_")
        if var_name and not var_name[0].isdigit():
            field_variables[var_name] = field_value
        else:
            # Handle edge cases where field name starts with digit or is empty
            field_variables[f"field_{var_name}"] = field_value

    # Print extracted variables for verification
    print("\nExtracted field variables:")
    for var_name, value in field_variables.items():
        print(f"{var_name} = '{value}'")

    # Create Excel file with field names and values
    create_excel_file(form_data, excel_output)

    print(f"\nProcess completed successfully!")
    print(f"Excel file saved as: {excel_output}")


if __name__ == "__main__":
    main()
