"""
PDF Form Data Extractor

This script extracts fillable form data from PDF files using pyPDFForm
and exports the field names and values to an Excel file using openpyxl.

Dependencies:
- pyPDFForm: For PDF form data extraction
- openpyxl: For Excel file creation and manipulation
"""

import os
from PyPDFForm import Pdf<PERSON>rapper
from openpyxl import Workbook


def extract_pdf_form_data(pdf_path):
    """
    Extract all fillable form data from a PDF file.

    Args:
        pdf_path (str): Path to the PDF file

    Returns:
        dict: Dictionary containing field names as keys and values as values
    """
    try:
        # Create PdfWrapper object
        pdf = PdfWrapper(pdf_path)

        # Get all form fields and their values using the data property
        form_data = pdf.data

        # Convert all values to strings and handle empty fields
        extracted_data = {}
        for field_name, field_value in form_data.items():
            # Convert to string, handle None/empty values
            if field_value is None:
                extracted_data[field_name] = ""
            else:
                extracted_data[field_name] = str(field_value)

        return extracted_data

    except Exception as e:
        print(f"Error extracting data from PDF: {e}")
        import traceback
        traceback.print_exc()
        return {}


def create_excel_file(data, output_path):
    """
    Create an Excel file with field names in column A and values in column B.
    
    Args:
        data (dict): Dictionary containing field names and values
        output_path (str): Path for the output Excel file
    """
    try:
        # Create a new workbook and select the active worksheet
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "PDF Form Data"
        
        # Add headers
        worksheet['A1'] = "Field Name"
        worksheet['B1'] = "Field Value"
        
        # Add data starting from row 2
        row = 2
        for field_name, field_value in data.items():
            worksheet[f'A{row}'] = field_name
            worksheet[f'B{row}'] = field_value
            row += 1
        
        # Auto-adjust column widths
        worksheet.column_dimensions['A'].width = 30
        worksheet.column_dimensions['B'].width = 50
        
        # Save the workbook
        workbook.save(output_path)
        print(f"Excel file created successfully: {output_path}")
        
    except Exception as e:
        print(f"Error creating Excel file: {e}")


def main():
    """
    Main function to extract PDF form data and create Excel file.
    """
    # Define file paths
    pdf_file = os.path.join("example forms", "MTM Loan App Starr.pdf")
    excel_output = "extracted_form_data.xlsx"

    # Check if PDF file exists
    if not os.path.exists(pdf_file):
        print(f"Error: PDF file not found at {pdf_file}")
        return

    print(f"Extracting data from: {pdf_file}")

    # Extract form data from PDF
    form_data = extract_pdf_form_data(pdf_file)

    if not form_data:
        print("No form data extracted or error occurred.")
        return

    print(f"Extracted {len(form_data)} form fields")

    # Create string variables for each field (as requested)
    # This creates individual variables for each field
    field_variables = {}
    for field_name, field_value in form_data.items():
        # Create a valid Python variable name from field name
        var_name = field_name.replace(" ", "_").replace("-", "_").replace(".", "_")
        var_name = "".join(c for c in var_name if c.isalnum() or c == "_")
        if var_name and not var_name[0].isdigit():
            field_variables[var_name] = field_value
        else:
            # Handle edge cases where field name starts with digit or is empty
            field_variables[f"field_{var_name}"] = field_value

    # Print extracted variables for verification
    print("\nExtracted field variables:")
    for var_name, value in field_variables.items():
        print(f"{var_name} = '{value}'")

    # Create Excel file with field names and values
    create_excel_file(form_data, excel_output)

    print(f"\nProcess completed successfully!")
    print(f"Excel file saved as: {excel_output}")


if __name__ == "__main__":
    main()
