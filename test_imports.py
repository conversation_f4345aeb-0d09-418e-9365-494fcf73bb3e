#!/usr/bin/env python3

print("Testing imports...")

try:
    import pyPDFForm
    print("✓ pyPDFForm imported successfully")
except ImportError as e:
    print(f"✗ Failed to import pyPDFForm: {e}")

try:
    import openpyxl
    print("✓ openpyxl imported successfully")
except ImportError as e:
    print(f"✗ Failed to import openpyxl: {e}")

try:
    from pyPDFForm import PdfWrapper
    print("✓ PdfWrapper imported successfully")
except ImportError as e:
    print(f"✗ Failed to import PdfWrapper: {e}")

import os
print(f"Current working directory: {os.getcwd()}")

# Check if the PDF file exists
pdf_path = os.path.join("example forms", "MTM Loan App Starr.pdf")
if os.path.exists(pdf_path):
    print(f"✓ PDF file found: {pdf_path}")
else:
    print(f"✗ PDF file not found: {pdf_path}")
    if os.path.exists("example forms"):
        print("Files in 'example forms' directory:")
        for file in os.listdir("example forms"):
            print(f"  - {file}")

print("Test completed.")
